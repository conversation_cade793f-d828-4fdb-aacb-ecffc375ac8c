'Attribute VB_Name = "modUtilities"
Option Explicit

' =========================================================
' 模块: modUtilities
' 描述: 提供通用工具函数，为其他模块提供支持
' =========================================================

' ---------------------------------------------------------
' Windows API声明用于DPI感知
' ---------------------------------------------------------
#If VBA7 Then
    Private Declare PtrSafe Function GetDC Lib "user32" (ByVal hwnd As LongPtr) As LongPtr
    Private Declare PtrSafe Function ReleaseDC Lib "user32" (ByVal hwnd As LongPtr, ByVal hdc As LongPtr) As Long
    Private Declare PtrSafe Function GetDeviceCaps Lib "gdi32" (ByVal hdc As LongPtr, ByVal nIndex As Long) As Long
    Private Declare PtrSafe Function GetActiveWindow Lib "user32" () As LongPtr
    Private Declare PtrSafe Function MonitorFromWindow Lib "user32" (ByVal hwnd As LongPtr, ByVal dwFlags As Long) As LongPtr
    Private Declare PtrSafe Function GetMonitorInfo Lib "user32" Alias "GetMonitorInfoA" (ByVal hMonitor As LongPtr, ByRef lpmi As MONITORINFO) As Long
#Else
    Private Declare Function GetDC Lib "user32" (ByVal hwnd As Long) As Long
    Private Declare Function ReleaseDC Lib "user32" (ByVal hwnd As Long, ByVal hdc As Long) As Long
    Private Declare Function GetDeviceCaps Lib "gdi32" (ByVal hdc As Long, ByVal nIndex As Long) As Long
    Private Declare Function GetActiveWindow Lib "user32" () As Long
    Private Declare Function MonitorFromWindow Lib "user32" (ByVal hwnd As Long, ByVal dwFlags As Long) As Long
    Private Declare Function GetMonitorInfo Lib "user32" Alias "GetMonitorInfoA" (ByVal hMonitor As Long, ByRef lpmi As MONITORINFO) As Long
#End If

' ---------------------------------------------------------
' 常量定义
' ---------------------------------------------------------
Private Const LOGPIXELSX = 88
Private Const LOGPIXELSY = 90
Private Const MONITOR_DEFAULTTONEAREST = &H2

' ---------------------------------------------------------
' 结构体定义
' ---------------------------------------------------------
Private Type RECT
    left As Long
    top As Long
    right As Long
    bottom As Long
End Type

Private Type MONITORINFO
    cbSize As Long
    rcMonitor As RECT
    rcWork As RECT
    dwFlags As Long
End Type

' ---------------------------------------------------------
' 日期处理函数
' ---------------------------------------------------------

' 计算两个日期之间的工作日数量
Public Function CalculateWorkingDays(startDate As Date, endDate As Date, Optional excludeWeekends As Boolean = True) As Long
    On Error GoTo ErrorHandler

    Dim days As Long
    Dim currentDate As Date

    days = 0
    currentDate = startDate

    ' 遍历每一天
    Do While currentDate <= endDate
        ' 检查是否为工作日
        If Not excludeWeekends Or (Weekday(currentDate) <> vbSaturday And Weekday(currentDate) <> vbSunday) Then
            days = days + 1
        End If

        ' 下一天
        currentDate = currentDate + 1
    Loop

    CalculateWorkingDays = days
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.CalculateWorkingDays"
    CalculateWorkingDays = 0 ' 返回默认值
End Function

' 获取日期的周数
Public Function GetWeekNumber(inputDate As Date) As String
    On Error GoTo ErrorHandler

    Dim weekNum As Integer

    ' 使用ISO 8601标准计算周数
    weekNum = DatePart("ww", inputDate, vbMonday, vbFirstFourDays)

    ' 格式化为"cwXX"
    GetWeekNumber = "cw" & Format(weekNum, "00")
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetWeekNumber"
    GetWeekNumber = "cw00" ' 返回默认值
End Function

' 格式化日期为指定格式的字符串
Public Function FormatDate(inputDate As Date, Optional formatString As String = "yyyy-mm-dd") As String
    On Error GoTo ErrorHandler

    FormatDate = Format(inputDate, formatString)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.FormatDate"
    FormatDate = "" ' 返回默认值
End Function

' ---------------------------------------------------------
' 单元格操作函数
' ---------------------------------------------------------

' 合并指定范围的单元格
Public Sub MergeCells(ws As Worksheet, rangeAddress As String)
    On Error GoTo ErrorHandler

    ws.Range(rangeAddress).Merge
    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.MergeCells"
End Sub

' 应用格式到指定范围
Public Sub ApplyFormat(range As Range, formatParams As Dictionary)
    On Error GoTo ErrorHandler

    ' 设置字体
    If formatParams.Exists("FontName") Then
        range.Font.Name = formatParams("FontName")
    End If

    If formatParams.Exists("FontSize") Then
        range.Font.Size = formatParams("FontSize")
    End If

    If formatParams.Exists("FontBold") Then
        range.Font.Bold = formatParams("FontBold")
    End If

    ' 设置颜色
    If formatParams.Exists("FontColor") Then
        range.Font.Color = formatParams("FontColor")
    End If

    If formatParams.Exists("BackColor") Then
        range.Interior.Color = formatParams("BackColor")
    End If

    ' 设置对齐
    If formatParams.Exists("HAlign") Then
        range.HorizontalAlignment = formatParams("HAlign")
    End If

    If formatParams.Exists("VAlign") Then
        range.VerticalAlignment = formatParams("VAlign")
    End If

    ' 设置边框
    If formatParams.Exists("BorderWeight") Then
        range.BorderAround Weight:=formatParams("BorderWeight")
    End If

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.ApplyFormat"
End Sub

' 获取指定行列的单元格地址
Public Function GetCellAddress(ws As Worksheet, row As Long, col As Long) As String
    On Error GoTo ErrorHandler

    GetCellAddress = ws.Cells(row, col).Address
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetCellAddress"
    GetCellAddress = "" ' 返回默认值
End Function

' ---------------------------------------------------------
' 错误处理函数
' ---------------------------------------------------------

' 记录错误信息到日志 (转发到modDebug.LogError)
Public Sub LogError(errNumber As Long, errDescription As String, errSource As String)
    On Error Resume Next ' 避免日志记录本身出错

    ' 直接调用modDebug模块的LogError函数
    modDebug.LogError errNumber, errDescription, errSource
End Sub

' 检查值是否在指定范围内
Public Function IsInRange(value As Variant, minValue As Variant, maxValue As Variant) As Boolean
    On Error GoTo ErrorHandler

    IsInRange = (value >= minValue And value <= maxValue)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.IsInRange"
    IsInRange = False ' 返回默认值
End Function

' ---------------------------------------------------------
' 其他工具函数
' ---------------------------------------------------------

' 检查值是否为空或Null
Public Function IsEmptyOrNull(value As Variant) As Boolean
    On Error GoTo ErrorHandler

    IsEmptyOrNull = (IsEmpty(value) Or IsNull(value) Or value = "")
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.IsEmptyOrNull"
    IsEmptyOrNull = True ' 返回默认值
End Function

' 去除字符串中的所有空白字符
Public Function TrimAll(text As String) As String
    On Error GoTo ErrorHandler

    TrimAll = Application.WorksheetFunction.Trim(text)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.TrimAll"
    TrimAll = "" ' 返回默认值
End Function

' 生成唯一ID
Public Function GetUniqueID(Optional prefix As String = "") As String
    On Error GoTo ErrorHandler

    Dim guid As String
    guid = Mid(CreateObject("Scriptlet.TypeLib").GUID, 2, 36)

    If prefix <> "" Then
        GetUniqueID = prefix & "_" & guid
    Else
        GetUniqueID = guid
    End If

    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetUniqueID"
    GetUniqueID = Format(Now, "yyyymmddhhmmss") ' 返回基于时间的ID作为备选
End Function

' 获取RGB颜色值
Public Function GetRGBColor(colorHex As String) As Long
    On Error GoTo ErrorHandler

    Dim r As Long, g As Long, b As Long

    ' 移除可能的#前缀
    If Left(colorHex, 1) = "#" Then
        colorHex = Mid(colorHex, 2)
    End If

    ' 解析十六进制颜色值
    r = CLng("&H" & Mid(colorHex, 1, 2))
    g = CLng("&H" & Mid(colorHex, 3, 2))
    b = CLng("&H" & Mid(colorHex, 5, 2))

    ' 返回RGB值
    GetRGBColor = RGB(r, g, b)
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetRGBColor"
    GetRGBColor = RGB(0, 0, 0) ' 返回黑色作为默认值
End Function

' ---------------------------------------------------------
' DPI和显示器相关函数
' ---------------------------------------------------------

' 获取DPI缩放因子
Public Function GetDPIScaleFactor() As Double
    On Error GoTo ErrorHandler

    modDebug.LogFunctionEntry "modUtilities.GetDPIScaleFactor"

    ' 检查是否启用DPI缩放修正
    Dim enableDPICorrection As Boolean
    enableDPICorrection = CBool(GetConfig("EnableDPICorrection", True))

    If Not enableDPICorrection Then
        modDebug.LogInfo "DPI缩放修正已禁用，返回默认缩放因子1.0", "modUtilities.GetDPIScaleFactor"
        GetDPIScaleFactor = 1#
        Exit Function
    End If

    ' 获取当前活动窗口的句柄
    #If VBA7 Then
        Dim hwnd As LongPtr
        Dim hdc As LongPtr
    #Else
        Dim hwnd As Long
        Dim hdc As Long
    #End If

    hwnd = GetActiveWindow()

    If hwnd = 0 Then
        ' 如果无法获取活动窗口，使用Excel应用程序窗口
        hwnd = Application.hwnd
    End If

    ' 获取设备上下文
    hdc = GetDC(hwnd)

    If hdc = 0 Then
        modDebug.LogWarning "无法获取设备上下文，使用默认DPI缩放因子", "modUtilities.GetDPIScaleFactor"
        GetDPIScaleFactor = 1#
        Exit Function
    End If

    ' 获取当前显示器的DPI
    Dim dpiX As Long, dpiY As Long
    dpiX = GetDeviceCaps(hdc, LOGPIXELSX)
    dpiY = GetDeviceCaps(hdc, LOGPIXELSY)

    ' 释放设备上下文
    ReleaseDC hwnd, hdc

    ' 计算缩放因子（基于标准96 DPI）
    Dim scaleFactor As Double
    scaleFactor = dpiX / 96#

    modDebug.LogInfo "检测到DPI: " & dpiX & " x " & dpiY & ", 缩放因子: " & scaleFactor, "modUtilities.GetDPIScaleFactor"

    ' 应用配置的DPI缩放调整因子
    Dim dpiAdjustmentFactor As Double
    dpiAdjustmentFactor = CDbl(GetConfig("DPIAdjustmentFactor", 1.0))

    scaleFactor = scaleFactor * dpiAdjustmentFactor

    modDebug.LogInfo "应用DPI调整因子 " & dpiAdjustmentFactor & " 后的最终缩放因子: " & scaleFactor, "modUtilities.GetDPIScaleFactor"

    GetDPIScaleFactor = scaleFactor
    modDebug.LogFunctionExit "modUtilities.GetDPIScaleFactor", "成功"
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetDPIScaleFactor"
    GetDPIScaleFactor = 1# ' 返回默认缩放因子
    modDebug.LogFunctionExit "modUtilities.GetDPIScaleFactor", "失败 - 返回默认值"
End Function

' 获取当前显示器信息
Public Function GetCurrentMonitorInfo() As Dictionary
    On Error GoTo ErrorHandler

    modDebug.LogFunctionEntry "modUtilities.GetCurrentMonitorInfo"

    Dim monitorInfo As New Dictionary

    ' 获取当前活动窗口的句柄
    #If VBA7 Then
        Dim hwnd As LongPtr
        Dim hMonitor As LongPtr
    #Else
        Dim hwnd As Long
        Dim hMonitor As Long
    #End If

    hwnd = GetActiveWindow()

    If hwnd = 0 Then
        ' 如果无法获取活动窗口，使用Excel应用程序窗口
        hwnd = Application.hwnd
    End If

    ' 获取窗口所在的显示器
    hMonitor = MonitorFromWindow(hwnd, MONITOR_DEFAULTTONEAREST)

    If hMonitor <> 0 Then
        Dim mi As MONITORINFO
        mi.cbSize = Len(mi)

        If GetMonitorInfo(hMonitor, mi) <> 0 Then
            ' 存储显示器信息
            monitorInfo.Add "MonitorLeft", mi.rcMonitor.left
            monitorInfo.Add "MonitorTop", mi.rcMonitor.top
            monitorInfo.Add "MonitorRight", mi.rcMonitor.right
            monitorInfo.Add "MonitorBottom", mi.rcMonitor.bottom
            monitorInfo.Add "MonitorWidth", mi.rcMonitor.right - mi.rcMonitor.left
            monitorInfo.Add "MonitorHeight", mi.rcMonitor.bottom - mi.rcMonitor.top
            monitorInfo.Add "WorkLeft", mi.rcWork.left
            monitorInfo.Add "WorkTop", mi.rcWork.top
            monitorInfo.Add "WorkRight", mi.rcWork.right
            monitorInfo.Add "WorkBottom", mi.rcWork.bottom
            monitorInfo.Add "WorkWidth", mi.rcWork.right - mi.rcWork.left
            monitorInfo.Add "WorkHeight", mi.rcWork.bottom - mi.rcWork.top
            monitorInfo.Add "IsPrimary", (mi.dwFlags And 1) = 1

            modDebug.LogInfo "获取显示器信息成功 - 分辨率: " & monitorInfo("MonitorWidth") & "x" & monitorInfo("MonitorHeight") & _
                            ", 主显示器: " & monitorInfo("IsPrimary"), "modUtilities.GetCurrentMonitorInfo"
        Else
            modDebug.LogWarning "无法获取显示器详细信息", "modUtilities.GetCurrentMonitorInfo"
        End If
    Else
        modDebug.LogWarning "无法获取显示器句柄", "modUtilities.GetCurrentMonitorInfo"
    End If

    Set GetCurrentMonitorInfo = monitorInfo
    modDebug.LogFunctionExit "modUtilities.GetCurrentMonitorInfo", "成功"
    Exit Function

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.GetCurrentMonitorInfo"
    Set GetCurrentMonitorInfo = New Dictionary ' 返回空字典
    modDebug.LogFunctionExit "modUtilities.GetCurrentMonitorInfo", "失败 - 返回空字典"
End Function

' 测试DPI缩放功能
Public Sub TestDPIScaling()
    On Error GoTo ErrorHandler

    modDebug.LogInfo "=== DPI缩放测试开始 ===", "modUtilities.TestDPIScaling"

    ' 获取DPI缩放因子
    Dim scaleFactor As Double
    scaleFactor = GetDPIScaleFactor()

    modDebug.LogInfo "当前DPI缩放因子: " & scaleFactor, "modUtilities.TestDPIScaling"

    ' 获取显示器信息
    Dim monitorInfo As Dictionary
    Set monitorInfo = GetCurrentMonitorInfo()

    If monitorInfo.Count > 0 Then
        modDebug.LogInfo "显示器信息:", "modUtilities.TestDPIScaling"
        modDebug.LogInfo "  分辨率: " & monitorInfo("MonitorWidth") & " x " & monitorInfo("MonitorHeight"), "modUtilities.TestDPIScaling"
        modDebug.LogInfo "  工作区: " & monitorInfo("WorkWidth") & " x " & monitorInfo("WorkHeight"), "modUtilities.TestDPIScaling"
        modDebug.LogInfo "  主显示器: " & monitorInfo("IsPrimary"), "modUtilities.TestDPIScaling"
    Else
        modDebug.LogWarning "无法获取显示器信息", "modUtilities.TestDPIScaling"
    End If

    ' 显示配置状态
    Dim enableDPICorrection As Boolean
    enableDPICorrection = CBool(GetConfig("EnableDPICorrection", True))
    modDebug.LogInfo "DPI缩放修正状态: " & IIf(enableDPICorrection, "启用", "禁用"), "modUtilities.TestDPIScaling"

    Dim dpiAdjustmentFactor As Double
    dpiAdjustmentFactor = CDbl(GetConfig("DPIAdjustmentFactor", 1.0))
    modDebug.LogInfo "DPI调整因子: " & dpiAdjustmentFactor, "modUtilities.TestDPIScaling"

    modDebug.LogInfo "=== DPI缩放测试完成 ===", "modUtilities.TestDPIScaling"

    ' 显示结果给用户
    MsgBox "DPI缩放测试完成！" & vbCrLf & _
           "当前DPI缩放因子: " & scaleFactor & vbCrLf & _
           "DPI修正状态: " & IIf(enableDPICorrection, "启用", "禁用") & vbCrLf & _
           "详细信息请查看调试日志。", vbInformation, "DPI缩放测试"

    Exit Sub

ErrorHandler:
    modDebug.LogError Err.Number, Err.Description, "modUtilities.TestDPIScaling"
    MsgBox "DPI缩放测试失败: " & Err.Description, vbCritical, "错误"
End Sub
